<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Negative Sentiment Table</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h5>Top Negative Sentiment Clients - Test</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="negativeSentimentTable">
                        <thead class="table-light">
                            <tr>
                                <th>Client</th>
                                <th>Current Sentiment</th>
                                <th>Trend</th>
                                <th>Last Update</th>
                            </tr>
                        </thead>
                        <tbody id="negativeSentimentTableBody">
                            <!-- Table content will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test data similar to what Django would provide
        const testSentimentData = {
            'Valdes ELIZABEHT': {
                current_sentiment: -0.31184363157416767,
                trend: 'declining',
                severity: 'medium',
                last_update: '2025-04-23',
                tickets: 13,
                resolution_time: 2.15,
                data_points: 3
            },
            'Sonja TAGHIPOUR': {
                current_sentiment: -0.30115102864801885,
                trend: 'stable',
                severity: 'medium',
                last_update: '2025-01-31',
                tickets: 5,
                resolution_time: 0.0,
                data_points: 1
            },
            'Ana Maria Galic': {
                current_sentiment: -0.24637378697904447,
                trend: 'declining',
                severity: 'medium',
                last_update: '2025-05-20',
                tickets: 12,
                resolution_time: 0.0,
                data_points: 3
            },
            'christina HUEBNER': {
                current_sentiment: -0.19219518452882767,
                trend: 'stable',
                severity: 'low',
                last_update: '2025-01-31',
                tickets: 5,
                resolution_time: 0.0,
                data_points: 1
            },
            'Wallmann GEROLD': {
                current_sentiment: -0.11544197176893552,
                trend: 'stable',
                severity: 'low',
                last_update: '2025-05-20',
                tickets: 3,
                resolution_time: 0.0,
                data_points: 3
            }
        };

        function populateNegativeSentimentTable() {
            const tableBody = document.getElementById('negativeSentimentTableBody');
            
            // Check if table body element exists
            if (!tableBody) {
                console.error('Table body element not found!');
                return;
            }

            // Helper function to get sentiment emoji
            function getSentimentEmoji(sentiment) {
                if (sentiment < -0.5) return '🚨'; // Critical
                if (sentiment < -0.4) return '🔴'; // High negative
                if (sentiment < -0.2) return '🟠'; // Medium negative
                if (sentiment < 0) return '🟡'; // Low negative
                return '⚪️'; // Neutral/Positive
            }

            // Helper function to get trend arrow
            function getTrendArrow(trend) {
                if (trend === 'improving') return '✅';
                if (trend === 'declining') return '❌';
                return '➖'; // Stable
            }

            // Convert to array and filter for negative sentiment only, then sort by sentiment (most negative first)
            const negativeSentimentClients = Object.entries(testSentimentData)
                .map(([client, data]) => {
                    return {
                        client: client,
                        sentiment: data.current_sentiment,
                        trend: data.trend,
                        severity: data.severity,
                        lastUpdate: data.last_update,
                        tickets: data.tickets,
                        resolutionTime: data.resolution_time,
                        dataPoints: data.data_points
                    };
                })
                .filter(client => client.sentiment < 0)  // Only negative sentiment
                .sort((a, b) => a.sentiment - b.sentiment);  // Most negative first

            // Take top 10 negative sentiment clients
            const topNegativeClients = negativeSentimentClients.slice(0, 10);

            // Clear existing table content
            tableBody.innerHTML = '';

            // Populate table with client data
            topNegativeClients.forEach(client => {
                const row = document.createElement('tr');
                
                // Format the last update date
                const lastUpdateDate = new Date(client.lastUpdate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                row.innerHTML = `
                    <td>
                        <strong>${client.client}</strong>
                    </td>
                    <td>
                        <span class="badge bg-light text-dark me-2">${client.sentiment.toFixed(3)}</span>
                        <span style="font-size: 1.2em;">${getSentimentEmoji(client.sentiment)}</span>
                    </td>
                    <td>
                        <span style="font-size: 1.2em;">${getTrendArrow(client.trend)}</span>
                        <span class="ms-2 text-muted">${client.trend}</span>
                    </td>
                    <td>
                        <small class="text-muted">${lastUpdateDate}</small>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
        }

        // Initialize table when page loads
        document.addEventListener('DOMContentLoaded', function() {
            populateNegativeSentimentTable();
        });
    </script>
</body>
</html>
